<div class="flex flex-col h-full w-screen bg-gray-200">
  <div
    class="flex justify-around items-center bg-[#041B30] py-4 border-b-4 shadow-2xl border-white"
  >
    <div class="text-center">
      <h1 class="text-5xl font-bold">EN COURS</h1>

      <p class="text-3xl mt-1">
        {{ preparingOrders.length === 0 ? "-" : preparingOrders.length }}

        COMMANDES
      </p>
    </div>
    <div
      class="absolute top-0 bg-[#041B30] border-4 shadow-2xl rounded-full p-2 text-center w-52 h-52 flex flex-col justify-center items-center"
    >
      <div class="text-7xl font-bold tracking-tight">
        {{ time }}
      </div>
      <div class="text-xl font-medium tracking-wide">
        {{ day }}
      </div>
    </div>
    <div class="text-center">
      <h1 class="text-5xl font-bold">C'EST PRÊT</h1>
      <p class="text-3xl mt-1">
        {{ readyOrders.length === 0 ? "-" : readyOrders.length }} COMMANDES
      </p>
    </div>
  </div>

  <div class="h-full flex justify-between px-6 py-4 flex-1 font-bold">
    <div *ngIf="preparingOrders.length >= 0 && !loading" class="w-[40%]">
      <div
        *ngFor="let item of preparingOrders; let index = index"
        class="rounded-xl overflow-hidden border-2 border-white mb-2"
      >
        <app-order-card
          [order]="item"
          background="bg-blue-500"
        ></app-order-card>
      </div>
    </div>

    <div *ngIf="readyOrders.length >= 0 && !loading" class="w-[40%] z-50">
      <div
        *ngFor="let item of readyOrders; let index = index"
        class="rounded-xl overflow-hidden border-2 border-white mb-2"
      >
        <app-order-card
          [order]="item"
          background="bg-green-500"
        ></app-order-card>
      </div>
    </div>
    <img
      class="absolute bottom-0 left-1/2 transform -translate-x-1/2 mb-2 z-10"
      width="286"
      height="61"
      src="../../../assets/images/qo-logo-horizontal.png"
      alt="Logo"
      style="opacity: 0.9"
    />
  </div>
</div>
