import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { ConfigService } from './config.service';
import { switchMap } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ApiService {

  constructor(private http: HttpClient, private configService: ConfigService) {}

  get<T>(endpoint: string) {
    return this.configService.getConfig().pipe(
      switchMap(config => {
        const apiUrl = config.server.apiUrl;
        const apiKey = config.server.apiKey;

        const headers = new HttpHeaders({
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`
        });

        return this.http.get<T>(`${apiUrl}${endpoint}`, { headers });
      })
    );
  }

  post<T>(endpoint: string, body: any) {
    return this.configService.getConfig().pipe(
      switchMap(config => {
        const apiUrl = config.server.apiUrl;
        const apiKey = config.server.apiKey;
        const headers = new HttpHeaders({
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`
        });
        return this.http.post<T>(`${apiUrl}${endpoint}`, body, { headers });
      })
    );
  }
}
