import { Injectable } from '@angular/core';

import { Observable, forkJoin, of } from 'rxjs';
import { ApiService } from './api.service';
import { map } from 'rxjs/operators';
import { ConfigService } from './config.service';
import { formatDate } from '@angular/common';
import { ActivatedRoute } from '@angular/router';

@Injectable({
  providedIn: 'root',
})
export class OrderService {
  users: string[] = [];
  branchId: number = 1;

  constructor(
    private apiService: ApiService,
    private configService: ConfigService,
    private route: ActivatedRoute
  ) {}

  fetchOrders(
    limit: number = 10
  ): Observable<{ preparing: any[]; ready: any[] }> {
    // Get today's date in YYYY-MM-DD format
    const today = new Date();
    const todayString = today.toISOString().split('T')[0];

    // Create start and end dates (00:00 and 23:59 of today)
    const startDate = `${todayString}T00:00:00`;
    const endDate = `${todayString}T23:59:59`;

    const preparingOrders$ = this.apiService.get(
      `/api/order/branch/${this.branchId}/filter?status=PREPARING&startDate=${startDate}&endDate=${endDate}`
    );

    const readyOrders$ = this.apiService.get(
      `/api/order/branch/${this.branchId}/filter?status=READY&startDate=${startDate}&endDate=${endDate}`
    );

    return forkJoin({
      preparing: preparingOrders$,
      ready: readyOrders$,
    }).pipe(
      map((response: any) => ({
        preparing: response.preparing.slice(0, limit).reverse(),
        ready: response.ready.slice(0, limit).reverse(),
      }))
    );
  }

  fetchOrdersByTableId(tableId: number): Observable<any> {
    return this.apiService.get(`/api/order?idTable=${tableId}`);
  }
}
