import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { RouterModule } from '@angular/router';
import { AppComponent } from './app.component';
import { DatePipe } from '@angular/common';
import { ScreenOrdersListComponent } from './components/screen-orders-list/screen-orders-list.component'; 
import { OrderCardComponent } from './components/order-card/order-card.component';
import { NgIconsModule } from '@ng-icons/core'; 
import { lucideShoppingBag, lucideUtensilsCrossed } from '@ng-icons/lucide';

@NgModule({ declarations: [
        AppComponent,
        ScreenOrdersListComponent,
        OrderCardComponent
    ],
    bootstrap: [AppComponent], imports: [BrowserModule,
        NgIconsModule.withIcons({ lucideShoppingBag, lucideUtensilsCrossed }),
        RouterModule.forRoot([])], providers: [DatePipe, provideHttpClient(withInterceptorsFromDi())] })
export class AppModule { }