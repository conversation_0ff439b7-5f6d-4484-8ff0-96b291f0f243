.pagination-buttons {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
}

.pagination-buttons button {
  padding: 0.5rem 1rem;
  margin: 0 0.5rem;
  background-color: #e2e8f0;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
}

.pagination-buttons button:disabled {
  background-color: #cbd5e0;
  cursor: not-allowed;
}

.pagination-buttons button:not(:disabled):hover {
  background-color: #a0aec0;
}

.scroll-container {
  display: flex;
  overflow-x: auto;
  width: 100%;
  min-height: 90%;
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); /* Adjust minmax values as needed */
  grid-auto-rows: min-content;
  gap: 16px; /* Adjust gap as needed */
  min-width: 100%; /* Ensure the grid takes the full width */
}

.flex-container {
  display: flex;
  flex-direction: column;
  height: 100vh; /* Full viewport height */
}

.flex-content {
  flex-grow: 1; /* Grow to take up remaining space */
} 

#scroll-style  {
  min-height: 93vh;
}

 
 
