import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { ConfigService } from '../../services/config.service';
import { OrderService } from '../../services/order.service';
import {
  trigger,
  transition,
  style,
  animate,
  query,
  stagger,
} from '@angular/animations';

@Component({
  selector: 'app-screen-orders-list',
  templateUrl: './screen-orders-list.component.html',
  styleUrls: ['./screen-orders-list.component.css'],
  animations: [
    trigger('listAnimation', [
      transition('* => *', [
        query(
          ':enter',
          [
            style({ opacity: 0, transform: 'translateY(-20px)' }),
            stagger(100, [
              animate(
                '300ms ease-out',
                style({ opacity: 1, transform: 'translateY(0)' })
              ),
            ]),
          ],
          { optional: true }
        ),
        query(
          ':leave',
          [
            stagger(100, [
              animate(
                '200ms ease-in',
                style({ opacity: 0, transform: 'translateX(100px)' })
              ),
            ]),
          ],
          { optional: true }
        ),
      ]),
    ]),
  ],
})
export class ScreenOrdersListComponent implements OnInit, OnDestroy {
  private intervalId: any;
  private intervalDateTimeId: any;
  refreshTime: number = 1000; // Default to 2 seconds
  orders: any[] = [];
  readyOrders: any[] = [];
  preparingOrders: any[] = [];
  loading: boolean = true;
  filteredOrders: any[] = [];
  day: string;
  time: string;

  constructor(
    private configService: ConfigService,
    private orderService: OrderService
  ) {}

  ngOnInit(): void {
    this.loadConfig();
    this.updateTime();
    this.intervalDateTimeId = setInterval(() => this.updateTime(), 1000);
    this.loadOrders();
  }

  ngOnDestroy(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
    if (this.intervalDateTimeId) {
      clearInterval(this.intervalDateTimeId);
    }
  }

  loadConfig(): void {
    this.configService.getConfig().subscribe((config) => {
      this.refreshTime = config.refreshTime || 1000; // Fallback to 2s
      this.startAutoRefresh();
    });
  }

  loadOrders(): void {
    this.orderService.fetchOrders(10).subscribe({
      next: (orders) => {
        // Use trackBy to help Angular identify changed items
        this.preparingOrders = this.mergeOrdersSmoothly(
          this.preparingOrders,
          orders.preparing
        );
        this.readyOrders = this.mergeOrdersSmoothly(
          this.readyOrders,
          orders.ready
        );
        this.loading = false;
      },
      error: (err) => {
        console.error('Error loading orders:', err);
        this.loading = false;
      },
    });
  }

  // Helper function to merge orders smoothly
  private mergeOrdersSmoothly(currentOrders: any[], newOrders: any[]): any[] {
    // Create a map of current orders for quick lookup
    const orderMap = new Map();
    currentOrders.forEach((order) => orderMap.set(order.id, order));

    // Update existing orders and add new ones
    return newOrders.map((newOrder) => {
      if (orderMap.has(newOrder.id)) {
        // If order exists, merge changes to maintain object reference when possible
        return { ...orderMap.get(newOrder.id), ...newOrder };
      }
      return newOrder;
    });
  }

  startAutoRefresh(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
    this.intervalId = setInterval(() => {
      this.loadOrders();
    }, this.refreshTime);
  }

  // Add trackBy function to help Angular optimize rendering
  trackByOrderId(index: number, order: any): string {
    return order.id;
  }

  formatCreationDate(date: string): string {
    return new Date(date).toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit',
    });
  }

  private updateTime(): void {
    const now = new Date();
    const days = [
      'DIMANCHE',
      'LUNDI',
      'MARDI',
      'MERCREDI',
      'JEUDI',
      'VENDREDI',
      'SAMEDI',
    ];
    const months = [
      'JANVIER',
      'FÉVRIER',
      'MARS',
      'AVRIL',
      'MAI',
      'JUIN',
      'JUILLET',
      'AOÛT',
      'SEPTEMBRE',
      'OCTOBRE',
      'NOVEMBRE',
      'DÉCEMBRE',
    ];

    this.day = `${now.getDate()} ${
      months[now.getMonth()]
    } ${now.getFullYear()}`;
    this.time = `${String(now.getHours()).padStart(2, '0')}:${String(
      now.getMinutes()
    ).padStart(2, '0')}`;
  }
}
