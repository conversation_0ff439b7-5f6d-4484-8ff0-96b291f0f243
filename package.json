{"name": "order-management", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve --port 3003", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^18.2.13", "@angular/common": "^18.2.13", "@angular/compiler": "^18.2.13", "@angular/core": "^18.2.13", "@angular/forms": "^18.2.13", "@angular/platform-browser": "^18.2.13", "@angular/platform-browser-dynamic": "^18.2.13", "@angular/router": "^18.2.13", "@ng-icons/core": "^30.2.0", "@ng-icons/lucide": "^30.2.0", "autoprefixer": "^10.4.19", "postcss": "^8.4.38", "rxjs": "~7.8.0", "tailwindcss": "^3.4.4", "tslib": "^2.3.0", "zone.js": "~0.14.10"}, "devDependencies": {"@angular-devkit/build-angular": "^18.2.19", "@angular/cli": "^18.2.19", "@angular/compiler-cli": "^18.2.13", "@types/jasmine": "~5.1.0", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.5.4"}}